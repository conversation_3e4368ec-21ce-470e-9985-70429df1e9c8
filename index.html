<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #2c3e50;
            font-family: Arial, sans-serif;
        }
        
        .game-container {
            text-align: center;
            background-color: #34495e;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
        }
        
        h1 {
            color: #ecf0f1;
            margin-bottom: 20px;
        }
        
        .score {
            color: #e74c3c;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        canvas {
            border: 3px solid #ecf0f1;
            background-color: #1a252f;
            display: block;
            margin: 0 auto;
        }
        
        .controls {
            margin-top: 20px;
            color: #ecf0f1;
        }
        
        .game-over {
            color: #e74c3c;
            font-size: 32px;
            font-weight: bold;
            margin: 20px 0;
        }
        
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        
        button:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>贪吃蛇游戏</h1>
        <div class="score">得分: <span id="score">0</span></div>
        <canvas id="gameCanvas" width="400" height="400"></canvas>
        <div id="gameOver" class="game-over" style="display: none;">游戏结束!</div>
        <button id="restartBtn" onclick="restartGame()" style="display: none;">重新开始</button>
        <div class="controls">
            <p>使用方向键控制蛇的移动</p>
            <p>吃到红色食物可以增长并得分</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        const gameOverElement = document.getElementById('gameOver');
        const restartBtn = document.getElementById('restartBtn');

        // 游戏设置
        const gridSize = 20;
        const tileCount = canvas.width / gridSize;

        let snake = [
            {x: 10, y: 10}
        ];
        let food = {};
        let dx = 0;
        let dy = 0;
        let score = 0;
        let gameRunning = true;

        // 生成随机食物位置
        function generateFood() {
            food = {
                x: Math.floor(Math.random() * tileCount),
                y: Math.floor(Math.random() * tileCount)
            };
        }

        // 绘制游戏元素
        function drawGame() {
            // 清空画布
            ctx.fillStyle = '#1a252f';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制蛇
            ctx.fillStyle = '#27ae60';
            for (let segment of snake) {
                ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2);
            }

            // 绘制食物
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 2, gridSize - 2);
        }

        // 移动蛇
        function moveSnake() {
            if (!gameRunning) return;

            const head = {x: snake[0].x + dx, y: snake[0].y + dy};

            // 检查边界碰撞
            if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
                gameOver();
                return;
            }

            // 检查自身碰撞
            for (let segment of snake) {
                if (head.x === segment.x && head.y === segment.y) {
                    gameOver();
                    return;
                }
            }

            snake.unshift(head);

            // 检查是否吃到食物
            if (head.x === food.x && head.y === food.y) {
                score += 10;
                scoreElement.textContent = score;
                generateFood();
            } else {
                snake.pop();
            }
        }

        // 游戏结束
        function gameOver() {
            gameRunning = false;
            gameOverElement.style.display = 'block';
            restartBtn.style.display = 'inline-block';
        }

        // 重新开始游戏
        function restartGame() {
            snake = [{x: 10, y: 10}];
            dx = 0;
            dy = 0;
            score = 0;
            scoreElement.textContent = score;
            gameRunning = true;
            gameOverElement.style.display = 'none';
            restartBtn.style.display = 'none';
            generateFood();
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameRunning) return;

            switch(e.key) {
                case 'ArrowUp':
                    if (dy !== 1) {
                        dx = 0;
                        dy = -1;
                    }
                    break;
                case 'ArrowDown':
                    if (dy !== -1) {
                        dx = 0;
                        dy = 1;
                    }
                    break;
                case 'ArrowLeft':
                    if (dx !== 1) {
                        dx = -1;
                        dy = 0;
                    }
                    break;
                case 'ArrowRight':
                    if (dx !== -1) {
                        dx = 1;
                        dy = 0;
                    }
                    break;
            }
        });

        // 游戏主循环
        function gameLoop() {
            moveSnake();
            drawGame();
        }

        // 初始化游戏
        generateFood();
        setInterval(gameLoop, 100);
    </script>
</body>
</html>
